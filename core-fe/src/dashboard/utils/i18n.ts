// Internationalization utilities for order validation
export type Language = 'en' | 'vi';

export interface TranslationMessages {
  en: string;
  vi: string;
}

export const defaultMessages = {
  orderLimitExceeded: {
    en: 'Order limit exceeded. Maximum allowed quantity is {maxQuantity}.',
    vi: 'Vượt quá giới hạn đặt hàng. Số lượng tối đa cho phép là {maxQuantity}.'
  },
  quantityTooHigh: {
    en: 'Quantity too high for product "{productName}". Maximum: {maxQuantity}, Current: {currentQuantity}.',
    vi: 'Số lượng quá cao cho sản phẩm "{productName}". Tối đa: {maxQuantity}, Hiện tại: {currentQuantity}.'
  },
  cannotProceedToCheckout: {
    en: 'Cannot proceed to checkout. Please adjust quantities to meet order limits.',
    vi: 'Không thể tiến hành thanh toán. Vui lòng điều chỉnh số lượng để đáp ứng giới hạn đặt hàng.'
  },
  checkoutBlocked: {
    en: 'Checkout is blocked due to order limit violations.',
    vi: '<PERSON>h toán bị chặn do vi phạm giới hạn đặt hàng.'
  },
  adjustQuantity: {
    en: 'Please adjust the quantity to {maxQuantity} or less.',
    vi: 'Vui lòng điều chỉnh số lượng xuống {maxQuantity} hoặc ít hơn.'
  },
  validationError: {
    en: 'Validation error occurred. Please try again.',
    vi: 'Đã xảy ra lỗi xác thực. Vui lòng thử lại.'
  },
  loadingValidation: {
    en: 'Validating order limits...',
    vi: 'Đang xác thực giới hạn đặt hàng...'
  },
  addToCartBlocked: {
    en: 'Cannot add to cart. This would exceed the order limit of {maxQuantity}.',
    vi: 'Không thể thêm vào giỏ hàng. Điều này sẽ vượt quá giới hạn đặt hàng {maxQuantity}.'
  },
  cartValidationFailed: {
    en: 'Cart validation failed. Some items exceed order limits.',
    vi: 'Xác thực giỏ hàng thất bại. Một số mặt hàng vượt quá giới hạn đặt hàng.'
  },
  proceedWithCaution: {
    en: 'Proceed with caution. Some items are near their order limits.',
    vi: 'Tiến hành một cách thận trọng. Một số mặt hàng gần đạt giới hạn đặt hàng.'
  }
};

export class I18nService {
  private currentLanguage: Language = 'en';

  setLanguage(language: Language): void {
    this.currentLanguage = language;
  }

  getCurrentLanguage(): Language {
    return this.currentLanguage;
  }

  translate(key: keyof typeof defaultMessages, params?: Record<string, string | number>): string {
    const message = defaultMessages[key];
    if (!message) {
      console.warn(`Translation key "${key}" not found`);
      return key;
    }

    let translatedText = message[this.currentLanguage];
    
    // Replace parameters in the message
    if (params) {
      Object.entries(params).forEach(([paramKey, paramValue]) => {
        const placeholder = `{${paramKey}}`;
        translatedText = translatedText.replace(new RegExp(placeholder, 'g'), String(paramValue));
      });
    }

    return translatedText;
  }

  translateMessage(message: TranslationMessages, params?: Record<string, string | number>): string {
    let translatedText = message[this.currentLanguage];
    
    if (params) {
      Object.entries(params).forEach(([paramKey, paramValue]) => {
        const placeholder = `{${paramKey}}`;
        translatedText = translatedText.replace(new RegExp(placeholder, 'g'), String(paramValue));
      });
    }

    return translatedText;
  }

  // Detect language from browser or URL
  detectLanguage(): Language {
    // Check URL parameters first
    const urlParams = new URLSearchParams(window.location.search);
    const langParam = urlParams.get('lang') as Language;
    if (langParam && ['en', 'vi'].includes(langParam)) {
      return langParam;
    }

    // Check browser language
    const browserLang = navigator.language.toLowerCase();
    if (browserLang.startsWith('vi')) {
      return 'vi';
    }

    return 'en'; // Default to English
  }

  // Auto-detect and set language
  autoDetectLanguage(): Language {
    const detectedLang = this.detectLanguage();
    this.setLanguage(detectedLang);
    return detectedLang;
  }
}

// Singleton instance
export const i18n = new I18nService();

// Helper function for quick translations
export const t = (key: keyof typeof defaultMessages, params?: Record<string, string | number>): string => {
  return i18n.translate(key, params);
};

// Helper function to get message in both languages
export const getBilingualMessage = (key: keyof typeof defaultMessages, params?: Record<string, string | number>): TranslationMessages => {
  const message = defaultMessages[key];
  if (!message) {
    return { en: key, vi: key };
  }

  const result: TranslationMessages = { en: message.en, vi: message.vi };
  
  if (params) {
    Object.entries(params).forEach(([paramKey, paramValue]) => {
      const placeholder = `{${paramKey}}`;
      result.en = result.en.replace(new RegExp(placeholder, 'g'), String(paramValue));
      result.vi = result.vi.replace(new RegExp(placeholder, 'g'), String(paramValue));
    });
  }

  return result;
};

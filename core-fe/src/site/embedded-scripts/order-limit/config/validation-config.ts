// Validation Configuration
// Manages order limit rules and validation settings

export interface OrderLimitRule {
  productId?: string;
  categoryId?: string;
  maxQuantity: number;
  minQuantity?: number;
  isGlobal?: boolean;
}

export interface ValidationSettings {
  enableProductPageValidation: boolean;
  enableCartPageValidation: boolean;
  enableCheckoutPageValidation: boolean;
  showWarningThreshold: number; // Show warning when quantity is within this threshold of limit
  validationDelay: number; // Delay in ms before validation triggers
}

export class ValidationConfig {
  private rules: OrderLimitRule[] = [];
  private settings: ValidationSettings;

  constructor() {
    this.settings = {
      enableProductPageValidation: true,
      enableCartPageValidation: true,
      enableCheckoutPageValidation: true,
      showWarningThreshold: 2,
      validationDelay: 300
    };

    // Default rules - these would typically come from backend/dashboard configuration
    this.initializeDefaultRules();
  }

  private initializeDefaultRules(): void {
    // Example default rules
    this.rules = [
      {
        isGlobal: true,
        maxQuantity: 10, // Global limit of 10 items per product
        minQuantity: 5 // Minimum 1 item required
      }
      // Add more specific rules as needed
    ];
  }

  getOrderLimitForProduct(productId: string, categoryId?: string): number {
    // Check for specific product rule first
    const productRule = this.rules.find((rule) => rule.productId === productId);
    if (productRule) {
      return productRule.maxQuantity;
    }

    // Check for category rule
    if (categoryId) {
      const categoryRule = this.rules.find((rule) => rule.categoryId === categoryId);
      if (categoryRule) {
        return categoryRule.maxQuantity;
      }
    }

    // Check for global rule
    const globalRule = this.rules.find((rule) => rule.isGlobal);
    if (globalRule) {
      return globalRule.maxQuantity;
    }

    // Default fallback
    return 999;
  }

  getMinQuantityForProduct(productId: string, categoryId?: string): number {
    // Check for specific product rule first
    const productRule = this.rules.find((rule) => rule.productId === productId);
    if (productRule && productRule.minQuantity !== undefined) {
      return productRule.minQuantity;
    }

    // Check for category rule
    if (categoryId) {
      const categoryRule = this.rules.find((rule) => rule.categoryId === categoryId);
      if (categoryRule && categoryRule.minQuantity !== undefined) {
        return categoryRule.minQuantity;
      }
    }

    // Check for global rule
    const globalRule = this.rules.find((rule) => rule.isGlobal);
    if (globalRule && globalRule.minQuantity !== undefined) {
      return globalRule.minQuantity;
    }

    // Default fallback
    return 1;
  }

  getSettings(): ValidationSettings {
    return { ...this.settings };
  }

  updateSettings(newSettings: Partial<ValidationSettings>): void {
    this.settings = { ...this.settings, ...newSettings };
  }

  addRule(rule: OrderLimitRule): void {
    this.rules.push(rule);
  }

  removeRule(index: number): void {
    if (index >= 0 && index < this.rules.length) {
      this.rules.splice(index, 1);
    }
  }

  getAllRules(): OrderLimitRule[] {
    return [...this.rules];
  }

  // Load configuration from external source (e.g., API, localStorage)
  async loadConfiguration(): Promise<void> {
    try {
      // Try to load from localStorage first
      const savedConfig = localStorage.getItem("wix-order-limit-config");
      if (savedConfig) {
        const config = JSON.parse(savedConfig);
        if (config.rules) {
          this.rules = config.rules;
        }
        if (config.settings) {
          this.settings = { ...this.settings, ...config.settings };
        }
      }
    } catch (error) {
      console.warn("Failed to load validation configuration:", error);
    }
  }

  // Save configuration to localStorage
  saveConfiguration(): void {
    try {
      const config = {
        rules: this.rules,
        settings: this.settings
      };
      localStorage.setItem("wix-order-limit-config", JSON.stringify(config));
    } catch (error) {
      console.warn("Failed to save validation configuration:", error);
    }
  }
}

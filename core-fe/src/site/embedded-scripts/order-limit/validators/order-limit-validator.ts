// Order Limit Validator
// Main validation logic for product, cart, and checkout pages

import { ValidationConfig } from "../config/validation-config";
import { DOMService, ProductInfo } from "../services/dom-service";
import { I18nService } from "../services/i18n-service";
import {
  ValidationResult,
  ValidationState,
  ValidationViolation,
  ValidationWarning
} from "../state/validation-state";

export class OrderLimitValidator {
  private config: ValidationConfig;
  private i18n: I18nService;
  private domService: DOMService;
  private state: ValidationState;
  private validationTimeout: number | null = null;

  constructor(
    config: ValidationConfig,
    i18n: I18nService,
    domService: DOMService,
    state: ValidationState
  ) {
    this.config = config;
    this.i18n = i18n;
    this.domService = domService;
    this.state = state;
  }

  // Product page validation
  async validateProductPage(): Promise<void> {
    console.log("Starting product page validation...");

    if (!this.config.getSettings().enableProductPageValidation) {
      console.log("Product page validation is disabled");
      return;
    }

    try {
      // Get product information
      const productInfo = this.domService.getProductInfo();
      if (!productInfo) {
        console.warn("Could not extract product information");
        return;
      }

      // Set up real-time validation
      this.setupProductPageListeners(productInfo);

      // Perform initial validation
      await this.validateProduct(productInfo);
    } catch (error) {
      console.error("Error in product page validation:", error);
      this.handleValidationError();
    }
  }

  private setupProductPageListeners(productInfo: ProductInfo): void {
    // Find quantity inputs and add change listeners
    const quantityInputs = document.querySelectorAll('input[type="number"]');
    quantityInputs.forEach((input) => {
      input.addEventListener("input", () => {
        this.debounceValidation(() => {
          const updatedInfo = {
            ...productInfo,
            currentQuantity: parseInt((input as HTMLInputElement).value, 10) || 1
          };
          this.validateProduct(updatedInfo);
        });
      });
    });

    // Observe DOM changes for dynamic content
    this.domService.observeElementChanges(document.body, () => {
      this.debounceValidation(() => {
        const updatedInfo = this.domService.getProductInfo();
        if (updatedInfo) {
          this.validateProduct(updatedInfo);
        }
      });
    });
  }

  private async validateProduct(productInfo: ProductInfo): Promise<void> {
    this.state.setValidating(true);

    try {
      const maxQuantity = this.config.getOrderLimitForProduct(
        productInfo.id,
        productInfo.categoryId
      );
      console.log(" validateProduct ~ maxQuantity:", maxQuantity);
      const violations: ValidationViolation[] = [];
      const warnings: ValidationWarning[] = [];

      // Check if current quantity exceeds limit
      if (productInfo.currentQuantity > maxQuantity) {
        const message = this.i18n.getMessage("quantityTooHigh", {
          productName: productInfo.name,
          maxQuantity: maxQuantity.toString(),
          currentQuantity: productInfo.currentQuantity.toString()
        });

        violations.push(
          this.state.createViolation(
            productInfo.id,
            productInfo.name,
            productInfo.currentQuantity,
            maxQuantity,
            message
          )
        );
      }
      // Check if approaching limit (warning)
      else if (
        productInfo.currentQuantity >
        maxQuantity - this.config.getSettings().showWarningThreshold
      ) {
        const message = this.i18n.getMessage("proceedWithCaution");

        warnings.push(
          this.state.createWarning(
            productInfo.id,
            productInfo.name,
            productInfo.currentQuantity,
            maxQuantity,
            message
          )
        );
      }

      const result = this.state.createValidationResult(violations, warnings);
      this.state.setValidationResult(result);

      // Apply UI changes
      this.applyProductPageValidation(result);
    } catch (error) {
      console.error("Error validating product:", error);
      this.handleValidationError();
    }
  }

  private applyProductPageValidation(result: ValidationResult): void {
    // Clear previous error messages and unblock elements
    this.clearPreviousValidation();

    if (!result.isValid) {
      // Block add to cart buttons
      const addToCartButtons = this.domService.getAddToCartButtons();
      addToCartButtons.forEach((button) => {
        const message = this.i18n.getMessage("addToCartBlocked", {
          maxQuantity: result.violations[0]?.maxQuantity.toString() || "0"
        });

        this.domService.blockElement(button, message);
        this.state.addBlockedElement(button);
      });

      // Show error messages
      result.violations.forEach((violation) => {
        const errorElement = this.domService.showErrorMessage(violation.message);
        this.state.addErrorMessage(errorElement);
      });
    }

    // Show warnings
    result.warnings.forEach((warning) => {
      const warningElement = this.domService.showErrorMessage(warning.message);
      warningElement.style.backgroundColor = "#ffa502"; // Orange for warnings
      this.state.addErrorMessage(warningElement);
    });
  }

  // Cart page validation
  async validateCartPage(): Promise<void> {
    console.log("Starting cart page validation...");

    if (!this.config.getSettings().enableCartPageValidation) {
      console.log("Cart page validation is disabled");
      return;
    }

    try {
      // Set up real-time validation
      this.setupCartPageListeners();

      // Perform initial validation
      await this.validateCart();
    } catch (error) {
      console.error("Error in cart page validation:", error);
      this.handleValidationError();
    }
  }

  private setupCartPageListeners(): void {
    // Find quantity inputs in cart and add change listeners
    const quantityInputs = document.querySelectorAll('input[type="number"]');
    quantityInputs.forEach((input) => {
      input.addEventListener("input", () => {
        this.debounceValidation(() => this.validateCart());
      });
    });

    // Observe cart changes
    this.domService.observeElementChanges(document.body, () => {
      this.debounceValidation(() => this.validateCart());
    });
  }

  private async validateCart(): Promise<void> {
    this.state.setValidating(true);

    try {
      const cartItems = this.domService.getCartItems();
      const violations: ValidationViolation[] = [];
      const warnings: ValidationWarning[] = [];

      for (const item of cartItems) {
        const maxQuantity = this.config.getOrderLimitForProduct(item.productId, item.categoryId);

        if (item.quantity > maxQuantity) {
          const message = this.i18n.getMessage("quantityTooHigh", {
            productName: item.productName,
            maxQuantity: maxQuantity.toString(),
            currentQuantity: item.quantity.toString()
          });

          violations.push(
            this.state.createViolation(
              item.productId,
              item.productName,
              item.quantity,
              maxQuantity,
              message
            )
          );
        } else if (item.quantity > maxQuantity - this.config.getSettings().showWarningThreshold) {
          const message = this.i18n.getMessage("proceedWithCaution");

          warnings.push(
            this.state.createWarning(
              item.productId,
              item.productName,
              item.quantity,
              maxQuantity,
              message
            )
          );
        }
      }

      const result = this.state.createValidationResult(violations, warnings);
      this.state.setValidationResult(result);

      // Apply UI changes
      this.applyCartPageValidation(result);
    } catch (error) {
      console.error("Error validating cart:", error);
      this.handleValidationError();
    }
  }

  private applyCartPageValidation(result: ValidationResult): void {
    // Clear previous validation
    this.clearPreviousValidation();

    if (!result.isValid) {
      // Block checkout buttons
      const checkoutButtons = this.domService.getCheckoutButtons();
      checkoutButtons.forEach((button) => {
        const message = this.i18n.getMessage("cannotProceedToCheckout");
        this.domService.blockElement(button, message);
        this.state.addBlockedElement(button);
      });

      // Show error message
      const errorMessage = this.i18n.getMessage("cartValidationFailed");
      const errorElement = this.domService.showErrorMessage(errorMessage);
      this.state.addErrorMessage(errorElement);

      // Show specific violations
      result.violations.forEach((violation) => {
        const errorElement = this.domService.showErrorMessage(violation.message);
        this.state.addErrorMessage(errorElement);
      });
    }

    // Show warnings
    result.warnings.forEach((warning) => {
      const warningElement = this.domService.showErrorMessage(warning.message);
      warningElement.style.backgroundColor = "#ffa502"; // Orange for warnings
      this.state.addErrorMessage(warningElement);
    });
  }

  // Checkout page validation
  async validateCheckoutPage(): Promise<void> {
    console.log("Starting checkout page validation...");

    if (!this.config.getSettings().enableCheckoutPageValidation) {
      console.log("Checkout page validation is disabled");
      return;
    }

    try {
      // Set up real-time validation
      this.setupCheckoutPageListeners();

      // Perform initial validation
      await this.validateCheckout();
    } catch (error) {
      console.error("Error in checkout page validation:", error);
      this.handleValidationError();
    }
  }

  private setupCheckoutPageListeners(): void {
    // Observe checkout page changes
    this.domService.observeElementChanges(document.body, () => {
      this.debounceValidation(() => this.validateCheckout());
    });

    // Listen for form submissions
    const forms = document.querySelectorAll("form");
    forms.forEach((form) => {
      form.addEventListener("submit", (event) => {
        if (this.state.hasViolations()) {
          event.preventDefault();
          event.stopPropagation();

          const message = this.i18n.getMessage("checkoutBlocked");
          this.domService.showErrorMessage(message);
        }
      });
    });
  }

  private async validateCheckout(): Promise<void> {
    this.state.setValidating(true);

    try {
      // For checkout page, we validate the same way as cart
      // since checkout contains the final cart items
      const cartItems = this.domService.getCartItems();
      const violations: ValidationViolation[] = [];
      const warnings: ValidationWarning[] = [];

      for (const item of cartItems) {
        const maxQuantity = this.config.getOrderLimitForProduct(item.productId, item.categoryId);

        if (item.quantity > maxQuantity) {
          const message = this.i18n.getMessage("quantityTooHigh", {
            productName: item.productName,
            maxQuantity: maxQuantity.toString(),
            currentQuantity: item.quantity.toString()
          });

          violations.push(
            this.state.createViolation(
              item.productId,
              item.productName,
              item.quantity,
              maxQuantity,
              message
            )
          );
        }
      }

      const result = this.state.createValidationResult(violations, warnings);
      this.state.setValidationResult(result);

      // Apply UI changes
      this.applyCheckoutPageValidation(result);
    } catch (error) {
      console.error("Error validating checkout:", error);
      this.handleValidationError();
    }
  }

  private applyCheckoutPageValidation(result: ValidationResult): void {
    // Clear previous validation
    this.clearPreviousValidation();

    if (!result.isValid) {
      // Block all checkout/payment buttons
      const checkoutButtons = this.domService.getCheckoutButtons();
      const paymentButtons = document.querySelectorAll(
        'button[class*="pay"], button[class*="complete"], button[class*="place-order"]'
      );

      [...checkoutButtons, ...Array.from(paymentButtons)].forEach((button) => {
        const message = this.i18n.getMessage("checkoutBlocked");
        this.domService.blockElement(button as HTMLElement, message);
        this.state.addBlockedElement(button as HTMLElement);
      });

      // Show error message
      const errorMessage = this.i18n.getMessage("checkoutBlocked");
      const errorElement = this.domService.showErrorMessage(errorMessage);
      this.state.addErrorMessage(errorElement);

      // Show specific violations
      result.violations.forEach((violation) => {
        const errorElement = this.domService.showErrorMessage(violation.message);
        this.state.addErrorMessage(errorElement);
      });
    }
  }

  // Utility methods
  private clearPreviousValidation(): void {
    // Unblock previously blocked elements
    this.state.getCurrentState().blockedElements.forEach((element) => {
      this.domService.unblockElement(element);
    });
    this.state.clearBlockedElements();

    // Remove previous error messages
    this.domService.removeErrorMessages();
    this.state.clearErrorMessages();
  }

  private debounceValidation(callback: () => void): void {
    if (this.validationTimeout) {
      clearTimeout(this.validationTimeout);
    }

    this.validationTimeout = window.setTimeout(() => {
      callback();
      this.validationTimeout = null;
    }, this.config.getSettings().validationDelay);
  }

  private handleValidationError(): void {
    this.state.setValidating(false);
    const errorMessage = this.i18n.getMessage("validationError");
    this.domService.showErrorMessage(errorMessage);
  }

  // Public methods for manual validation
  async forceValidation(): Promise<ValidationResult | null> {
    const currentPage = this.domService.detectCurrentPage();

    switch (currentPage) {
      case "product":
        await this.validateProductPage();
        break;
      case "cart":
        await this.validateCartPage();
        break;
      case "checkout":
        await this.validateCheckoutPage();
        break;
    }

    return this.state.getLastValidation();
  }

  getCurrentValidationState(): ValidationResult | null {
    return this.state.getLastValidation();
  }
}

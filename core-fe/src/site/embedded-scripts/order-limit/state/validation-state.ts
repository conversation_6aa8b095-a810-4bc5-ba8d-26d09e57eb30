// Validation State Management
// Manages the current state of validation across different pages

export interface ValidationResult {
  isValid: boolean;
  violations: ValidationViolation[];
  warnings: ValidationWarning[];
}

export interface ValidationViolation {
  productId: string;
  productName: string;
  currentQuantity: number;
  maxQuantity: number;
  message: string;
  severity: "error" | "warning";
}

export interface ValidationWarning {
  productId: string;
  productName: string;
  currentQuantity: number;
  maxQuantity: number;
  message: string;
}

export interface PageValidationState {
  isValidating: boolean;
  lastValidation: ValidationResult | null;
  blockedElements: HTMLElement[];
  errorMessages: HTMLElement[];
  lastInputValues: Map<string, number>; // Track last input values to detect changes
  hasShownMessage: boolean; // Track if message has been shown for current state
}

export class ValidationState {
  private state: PageValidationState;
  private listeners: Array<(state: PageValidationState) => void> = [];

  constructor() {
    this.state = {
      isValidating: false,
      lastValidation: null,
      blockedElements: [],
      errorMessages: [],
      lastInputValues: new Map(),
      hasShownMessage: false
    };
  }

  // State getters
  getCurrentState(): PageValidationState {
    return { ...this.state };
  }

  isValidating(): boolean {
    return this.state.isValidating;
  }

  getLastValidation(): ValidationResult | null {
    return this.state.lastValidation;
  }

  hasViolations(): boolean {
    return (this.state.lastValidation?.violations.length ?? 0) > 0;
  }

  hasWarnings(): boolean {
    return (this.state.lastValidation?.warnings.length ?? 0) > 0;
  }

  getViolations(): ValidationViolation[] {
    return this.state.lastValidation?.violations || [];
  }

  getWarnings(): ValidationWarning[] {
    return this.state.lastValidation?.warnings || [];
  }

  // State setters
  setValidating(isValidating: boolean): void {
    this.updateState({ isValidating });
  }

  setValidationResult(result: ValidationResult): void {
    this.updateState({
      lastValidation: result,
      isValidating: false
    });
  }

  addBlockedElement(element: HTMLElement): void {
    if (!this.state.blockedElements.includes(element)) {
      this.updateState({
        blockedElements: [...this.state.blockedElements, element]
      });
    }
  }

  removeBlockedElement(element: HTMLElement): void {
    this.updateState({
      blockedElements: this.state.blockedElements.filter((el) => el !== element)
    });
  }

  clearBlockedElements(): void {
    this.updateState({ blockedElements: [] });
  }

  addErrorMessage(element: HTMLElement): void {
    if (!this.state.errorMessages.includes(element)) {
      this.updateState({
        errorMessages: [...this.state.errorMessages, element]
      });
    }
  }

  removeErrorMessage(element: HTMLElement): void {
    this.updateState({
      errorMessages: this.state.errorMessages.filter((el) => el !== element)
    });
  }

  clearErrorMessages(): void {
    this.updateState({ errorMessages: [] });
  }

  // Input value tracking methods
  setInputValue(inputId: string, value: number): void {
    this.state.lastInputValues.set(inputId, value);
  }

  getInputValue(inputId: string): number | undefined {
    return this.state.lastInputValues.get(inputId);
  }

  hasInputChanged(inputId: string, currentValue: number): boolean {
    const lastValue = this.getInputValue(inputId);
    return lastValue === undefined || lastValue !== currentValue;
  }

  // Message state tracking
  setMessageShown(shown: boolean): void {
    this.updateState({ hasShownMessage: shown });
  }

  hasMessageBeenShown(): boolean {
    return this.state.hasShownMessage;
  }

  shouldShowMessage(inputId: string, currentValue: number): boolean {
    // Show message if input has changed or no message has been shown yet
    return this.hasInputChanged(inputId, currentValue) || !this.hasMessageBeenShown();
  }

  // Validation result helpers
  createValidationResult(
    violations: ValidationViolation[] = [],
    warnings: ValidationWarning[] = []
  ): ValidationResult {
    return {
      isValid: violations.length === 0,
      violations,
      warnings
    };
  }

  createViolation(
    productId: string,
    productName: string,
    currentQuantity: number,
    maxQuantity: number,
    message: string,
    severity: "error" | "warning" = "error"
  ): ValidationViolation {
    return {
      productId,
      productName,
      currentQuantity,
      maxQuantity,
      message,
      severity
    };
  }

  createWarning(
    productId: string,
    productName: string,
    currentQuantity: number,
    maxQuantity: number,
    message: string
  ): ValidationWarning {
    return {
      productId,
      productName,
      currentQuantity,
      maxQuantity,
      message
    };
  }

  // State management
  private updateState(updates: Partial<PageValidationState>): void {
    this.state = { ...this.state, ...updates };
    this.notifyListeners();
  }

  // Event listeners
  addListener(listener: (state: PageValidationState) => void): void {
    this.listeners.push(listener);
  }

  removeListener(listener: (state: PageValidationState) => void): void {
    this.listeners = this.listeners.filter((l) => l !== listener);
  }

  private notifyListeners(): void {
    this.listeners.forEach((listener) => {
      try {
        listener(this.state);
      } catch (error) {
        console.error("Error in validation state listener:", error);
      }
    });
  }

  // Reset state
  reset(): void {
    this.state = {
      isValidating: false,
      lastValidation: null,
      blockedElements: [],
      errorMessages: [],
      lastInputValues: new Map(),
      hasShownMessage: false
    };
    this.notifyListeners();
  }

  // Utility methods
  getViolationsByProduct(productId: string): ValidationViolation[] {
    return this.getViolations().filter((v) => v.productId === productId);
  }

  getWarningsByProduct(productId: string): ValidationWarning[] {
    return this.getWarnings().filter((w) => w.productId === productId);
  }

  hasViolationsForProduct(productId: string): boolean {
    return this.getViolationsByProduct(productId).length > 0;
  }

  hasWarningsForProduct(productId: string): boolean {
    return this.getWarningsByProduct(productId).length > 0;
  }

  // Summary methods
  getTotalViolations(): number {
    return this.getViolations().length;
  }

  getTotalWarnings(): number {
    return this.getWarnings().length;
  }

  getValidationSummary(): string {
    const violations = this.getTotalViolations();
    const warnings = this.getTotalWarnings();

    if (violations > 0) {
      return `${violations} validation error${violations > 1 ? "s" : ""}`;
    }

    if (warnings > 0) {
      return `${warnings} warning${warnings > 1 ? "s" : ""}`;
    }

    return "All validations passed";
  }

  // Debug methods
  logCurrentState(): void {
    console.log("Current Validation State:", {
      isValidating: this.state.isValidating,
      hasViolations: this.hasViolations(),
      hasWarnings: this.hasWarnings(),
      violationsCount: this.getTotalViolations(),
      warningsCount: this.getTotalWarnings(),
      blockedElementsCount: this.state.blockedElements.length,
      errorMessagesCount: this.state.errorMessages.length,
      summary: this.getValidationSummary()
    });
  }
}

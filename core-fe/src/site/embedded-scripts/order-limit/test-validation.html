<!DOCTYPE html>
<html lang="vi">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Test Order Limit Validation System</title>
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
        line-height: 1.6;
        background-color: #f5f5f5;
      }

      .test-container {
        background: white;
        border-radius: 8px;
        padding: 20px;
        margin: 20px 0;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .test-section {
        margin: 30px 0;
        padding: 20px;
        border: 1px solid #ddd;
        border-radius: 8px;
        background: #f9f9f9;
      }

      .test-section h2 {
        margin-top: 0;
        color: #333;
        border-bottom: 2px solid #007bff;
        padding-bottom: 10px;
      }

      .product-info,
      .cart-item {
        background: white;
        padding: 15px;
        border-radius: 4px;
        margin: 10px 0;
        border-left: 4px solid #007bff;
      }

      .quantity-input {
        padding: 8px;
        border: 1px solid #ccc;
        border-radius: 4px;
        width: 80px;
        margin: 0 10px;
        font-size: 16px;
      }

      .add-to-cart-button,
      .checkout-button {
        background: #007bff;
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 16px;
        margin: 10px 5px;
        transition: background-color 0.3s;
      }

      .add-to-cart-button:hover,
      .checkout-button:hover {
        background: #0056b3;
      }

      .language-switcher {
        position: fixed;
        top: 20px;
        right: 20px;
        background: white;
        padding: 10px;
        border-radius: 4px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        z-index: 1000;
      }

      .language-switcher button {
        margin: 0 5px;
        padding: 5px 10px;
        border: 1px solid #ccc;
        background: white;
        cursor: pointer;
        border-radius: 3px;
      }

      .language-switcher button.active {
        background: #007bff;
        color: white;
      }

      .debug-info {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        padding: 15px;
        margin: 20px 0;
        font-family: monospace;
        font-size: 14px;
      }

      .status-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 8px;
      }

      .status-success {
        background-color: #28a745;
      }
      .status-warning {
        background-color: #ffc107;
      }
      .status-error {
        background-color: #dc3545;
      }
      .status-loading {
        background-color: #6c757d;
      }

      .test-controls {
        background: #e9ecef;
        padding: 15px;
        border-radius: 4px;
        margin: 10px 0;
      }

      .test-controls button {
        background: #28a745;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
        margin: 5px;
      }

      .test-controls button:hover {
        background: #218838;
      }

      /* Order Limit Validation Styles */
      .wix-order-limit-error {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu,
          Cantarell, sans-serif;
        line-height: 1.4;
        animation: slideIn 0.3s ease-out;
      }

      .wix-order-limit-error.warning {
        background-color: #ffa502 !important;
      }

      @keyframes slideIn {
        from {
          opacity: 0;
          transform: translateY(-10px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      /* Blocked element styles */
      [data-blocked-reason] {
        position: relative;
        cursor: not-allowed !important;
      }

      [data-blocked-reason]:hover::after {
        content: attr(data-blocked-reason);
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(0, 0, 0, 0.9);
        color: white;
        padding: 8px 12px;
        border-radius: 4px;
        font-size: 12px;
        white-space: nowrap;
        z-index: 10000;
        pointer-events: none;
      }
    </style>
  </head>
  <body>
    <div class="language-switcher">
      <button onclick="setLanguage('en')" id="lang-en">English</button>
      <button onclick="setLanguage('vi')" id="lang-vi" class="active">Tiếng Việt</button>
    </div>

    <div class="test-container">
      <h1>🛒 Hệ Thống Kiểm Tra Giới Hạn Đặt Hàng</h1>
      <p>
        Trang này mô phỏng các trang Wix khác nhau để kiểm tra hệ thống validation giới hạn đặt
        hàng.
      </p>

      <div class="debug-info">
        <h3>📊 Thông Tin Debug</h3>
        <div id="debug-output">
          <p>
            <span class="status-indicator status-loading"></span>Trạng thái hệ thống:
            <span id="system-status">Đang tải...</span>
          </p>
          <p>
            <span class="status-indicator status-loading"></span>Loại trang hiện tại:
            <span id="page-type">Đang phát hiện...</span>
          </p>
          <p>
            <span class="status-indicator status-loading"></span>Ngôn ngữ hiện tại:
            <span id="current-language">Đang phát hiện...</span>
          </p>
          <p>
            <span class="status-indicator status-loading"></span>Kết quả validation cuối:
            <span id="validation-result">Chưa có</span>
          </p>
          <p>
            <span class="status-indicator status-loading"></span>Giới hạn đặt hàng:
            <span id="order-limit">Min: 1, Max: 10 sản phẩm (mặc định)</span>
          </p>
        </div>
      </div>
    </div>

    <!-- Product Page Simulation -->
    <div class="test-container" id="product-page-test">
      <div class="test-section">
        <h2>🛍️ Test Trang Sản Phẩm</h2>
        <p>Mô phỏng trang sản phẩm với input số lượng và nút thêm vào giỏ hàng.</p>

        <div class="product-info">
          <h3 data-testid="product-title">Tai Nghe Bluetooth Cao Cấp</h3>
          <p>Giá: 2.500.000 VNĐ</p>
          <p>
            Số lượng:
            <input
              type="number"
              class="quantity-input"
              value="1"
              min="1"
              max="20"
              data-testid="quantity-input"
            />
            <button class="add-to-cart-button" data-testid="add-to-cart-button">
              Thêm Vào Giỏ Hàng
            </button>
          </p>
          <p>
            <small
              >⚠️ Giới hạn đặt hàng: Tối thiểu 1, Tối đa 10 sản phẩm (được cấu hình trong hệ thống
              validation)</small
            >
          </p>
        </div>

        <div class="test-controls">
          <button onclick="simulateProductPage()">🔄 Mô Phỏng Trang Sản Phẩm</button>
          <button onclick="setQuantity(15)">⚡ Đặt Số Lượng = 15 (Vượt Giới Hạn Max)</button>
          <button onclick="setQuantity(0)">🚫 Đặt Số Lượng = 0 (Dưới Giới Hạn Min)</button>
          <button onclick="setQuantity(8)">⚠️ Đặt Số Lượng = 8 (Gần Giới Hạn)</button>
          <button onclick="setQuantity(5)">✅ Đặt Số Lượng = 5 (An Toàn)</button>
        </div>
      </div>
    </div>

    <!-- Cart Page Simulation -->
    <div class="test-container" id="cart-page-test">
      <div class="test-section">
        <h2>🛒 Test Trang Giỏ Hàng</h2>
        <p>Mô phỏng trang giỏ hàng với nhiều sản phẩm và nút thanh toán.</p>

        <div class="cart-item" data-testid="cart-item">
          <h4 data-testid="product-name">Tai Nghe Bluetooth Cao Cấp</h4>
          <p>
            Số lượng:
            <input
              type="number"
              class="quantity-input"
              value="5"
              min="1"
              max="20"
              data-product-id="product_1"
            />
          </p>
        </div>

        <div class="cart-item" data-testid="cart-item">
          <h4 data-testid="product-name">Loa Bluetooth Mini</h4>
          <p>
            Số lượng:
            <input
              type="number"
              class="quantity-input"
              value="3"
              min="1"
              max="20"
              data-product-id="product_2"
            />
          </p>
        </div>

        <div class="test-controls">
          <button class="checkout-button" data-testid="checkout-button">
            Tiến Hành Thanh Toán
          </button>
          <button onclick="simulateCartPage()">🔄 Mô Phỏng Trang Giỏ Hàng</button>
          <button onclick="setCartQuantity(12)">
            ⚡ Đặt Số Lượng Tai Nghe = 12 (Vượt Giới Hạn)
          </button>
        </div>
      </div>
    </div>

    <!-- Checkout Page Simulation -->
    <div class="test-container" id="checkout-page-test">
      <div class="test-section">
        <h2>💳 Test Trang Thanh Toán</h2>
        <p>Mô phỏng trang thanh toán với validation cuối cùng.</p>

        <div class="cart-item">
          <h4>📋 Tóm Tắt Đơn Hàng</h4>
          <p>
            Tai Nghe Bluetooth Cao Cấp x <span id="checkout-quantity">12</span> (vượt giới hạn 10)
          </p>
          <p>Loa Bluetooth Mini x 3</p>
          <p>
            <strong>Tổng cộng: <span id="total-amount">37.500.000 VNĐ</span></strong>
          </p>
        </div>

        <form onsubmit="return false;">
          <div class="test-controls">
            <button type="submit" class="checkout-button" data-testid="complete-order">
              Hoàn Tất Đơn Hàng
            </button>
            <button class="checkout-button" data-testid="pay-now">Thanh Toán Ngay</button>
            <button onclick="simulateCheckoutPage()">🔄 Mô Phỏng Trang Thanh Toán</button>
          </div>
        </form>
      </div>
    </div>

    <script type="module">
      // Import and initialize the validation system
      import { ValidationConfig } from "./config/validation-config.js";
      import { I18nService } from "./services/i18n-service.js";
      import { DOMService } from "./services/dom-service.js";
      import { ValidationState } from "./state/validation-state.js";
      import { OrderLimitValidator } from "./validators/order-limit-validator.js";

      class OrderLimitSystem {
        constructor() {
          this.config = new ValidationConfig();
          this.i18n = new I18nService();
          this.domService = new DOMService();
          this.state = new ValidationState();
          this.validator = new OrderLimitValidator(
            this.config,
            this.i18n,
            this.domService,
            this.state
          );
        }

        async initialize() {
          try {
            console.log("Initializing Order Limit Validation System...");

            await this.domService.waitForDOM();
            await this.i18n.initialize();
            await this.startValidation();

            console.log("Order Limit Validation System initialized successfully");
            updateSystemStatus("Đã khởi tạo thành công", "success");
          } catch (error) {
            console.error("Failed to initialize Order Limit Validation System:", error);
            updateSystemStatus("Lỗi khởi tạo: " + error.message, "error");
          }
        }

        async startValidation() {
          const currentPage = this.domService.detectCurrentPage();

          switch (currentPage) {
            case "product":
              await this.validator.validateProductPage();
              break;
            case "cart":
              await this.validator.validateCartPage();
              break;
            case "checkout":
              await this.validator.validateCheckoutPage();
              break;
            default:
              console.log("No validation needed for current page:", currentPage);
          }
        }
      }

      // Initialize the system
      const system = new OrderLimitSystem();
      window.orderLimitSystem = system;
      system.initialize();

      // Helper functions for status updates
      function updateSystemStatus(message, type) {
        const statusElement = document.getElementById("system-status");
        const indicator = statusElement.previousElementSibling;

        statusElement.textContent = message;
        indicator.className = `status-indicator status-${type}`;
      }

      function updateValidationResult(result) {
        const resultElement = document.getElementById("validation-result");
        const indicator = resultElement.previousElementSibling;

        if (result && result.violations && result.violations.length > 0) {
          resultElement.textContent = `${result.violations.length} lỗi validation`;
          indicator.className = "status-indicator status-error";
        } else if (result && result.warnings && result.warnings.length > 0) {
          resultElement.textContent = `${result.warnings.length} cảnh báo`;
          indicator.className = "status-indicator status-warning";
        } else {
          resultElement.textContent = "Tất cả validation đã pass";
          indicator.className = "status-indicator status-success";
        }
      }

      // Monitor validation state changes
      setInterval(() => {
        if (window.orderLimitSystem) {
          const result = window.orderLimitSystem.validator.getCurrentValidationState();
          updateValidationResult(result);
        }
      }, 1000);
    </script>

    <script>
      // Language switching functionality
      function setLanguage(lang) {
        localStorage.setItem("wix-order-limit-language", lang);
        document.documentElement.lang = lang;

        // Update button states
        document.querySelectorAll(".language-switcher button").forEach((btn) => {
          btn.classList.remove("active");
        });
        document.getElementById(`lang-${lang}`).classList.add("active");

        updateDebugInfo();

        // Trigger re-initialization if validation system is loaded
        if (window.orderLimitSystem) {
          window.orderLimitSystem.initialize();
        }
      }

      // Page simulation functions
      function simulateProductPage() {
        history.pushState({}, "", "/product/tai-nghe-bluetooth");
        updateDebugInfo();
        console.log("Simulating product page...");
        if (window.orderLimitSystem) {
          window.orderLimitSystem.startValidation();
        }
      }

      function simulateCartPage() {
        history.pushState({}, "", "/cart");
        updateDebugInfo();
        console.log("Simulating cart page...");
        if (window.orderLimitSystem) {
          window.orderLimitSystem.startValidation();
        }
      }

      function simulateCheckoutPage() {
        history.pushState({}, "", "/checkout");
        updateDebugInfo();
        console.log("Simulating checkout page...");
        if (window.orderLimitSystem) {
          window.orderLimitSystem.startValidation();
        }
      }

      // Quantity manipulation functions
      function setQuantity(quantity) {
        const quantityInput = document.querySelector("#product-page-test .quantity-input");
        if (quantityInput) {
          quantityInput.value = quantity;
          quantityInput.dispatchEvent(new Event("input", { bubbles: true }));
        }
      }

      function setCartQuantity(quantity) {
        const cartQuantityInput = document.querySelector("#cart-page-test .quantity-input");
        if (cartQuantityInput) {
          cartQuantityInput.value = quantity;
          cartQuantityInput.dispatchEvent(new Event("input", { bubbles: true }));
        }
      }

      // Debug information updates
      function updateDebugInfo() {
        document.getElementById("page-type").textContent = detectPageType();
        document.getElementById("current-language").textContent = getCurrentLanguage();
      }

      function detectPageType() {
        const url = window.location.href.toLowerCase();
        if (url.includes("/product/")) return "product";
        if (url.includes("/cart")) return "cart";
        if (url.includes("/checkout")) return "checkout";
        return "other";
      }

      function getCurrentLanguage() {
        return (
          localStorage.getItem("wix-order-limit-language") || document.documentElement.lang || "vi"
        );
      }

      // Initialize debug info
      document.addEventListener("DOMContentLoaded", () => {
        updateDebugInfo();

        // Set up quantity input listeners for testing
        document.querySelectorAll(".quantity-input").forEach((input) => {
          input.addEventListener("input", () => {
            console.log("Quantity changed:", input.value);
            updateDebugInfo();
          });
        });

        // Set up button click listeners
        document.querySelectorAll(".add-to-cart-button, .checkout-button").forEach((button) => {
          button.addEventListener("click", (e) => {
            console.log("Button clicked:", button.textContent);
            if (button.disabled) {
              e.preventDefault();
              console.log("Button is disabled due to validation");
            }
          });
        });
      });

      // Start with product page simulation
      setTimeout(() => {
        simulateProductPage();
      }, 1000);
    </script>
  </body>
</html>

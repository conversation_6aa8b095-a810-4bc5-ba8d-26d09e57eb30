# 🛒 Hệ Thống Kiểm Tra Giới Hạn Đặt Hàng (Order Limit Validation System)

Một hệ thống embedded script toàn diện cho các trang Wix để kiểm tra giới hạn đặt hàng trên các trang sản phẩm, giỏ hàng và thanh toán với hỗ trợ song ngữ (Tiếng Việt/English).

## ✨ Tính Năng Chính

### ✅ **Validation Đa Trang**
- **Trang Sản Phẩm**: Kiểm tra số lượng trước khi thêm vào giỏ, chặn nút "Thêm Vào Giỏ" khi vượt giới hạn
- **Trang Giỏ Hàng**: Kiểm tra các sản phẩm trong giỏ, chặn nút thanh toán khi vượt giới hạn  
- **Trang Thanh Toán**: Validation cuối cùng, chặn hoàn tất thanh toán khi vượt giới hạn

### ✅ **Hỗ Trợ Song Ngữ**
- **Tiếng Việt (vi)**: <PERSON><PERSON><PERSON> đủ bản dịch tiếng Việt cho tất cả thông báo lỗi
- **English (en)**: Bản dịch tiếng Anh làm fallback
- **Tự động phát hiện**: Tự động phát hiện ngôn ngữ từ URL, thuộc tính HTML lang, cài đặt trình duyệt, hoặc nội dung trang

### ✅ **Validation Thời Gian Thực**
- **Debounced validation**: Ngăn chặn các API call quá mức với delay có thể cấu hình
- **DOM observation**: Tự động phát hiện thay đổi trong input số lượng và cập nhật giỏ hàng
- **Event-driven**: Phản hồi với tương tác người dùng theo thời gian thực

### ✅ **Trải Nghiệm Người Dùng**
- **Visual feedback**: Thông báo lỗi rõ ràng với animation mượt mà
- **Button blocking**: Vô hiệu hóa button với tooltip giải thích lý do
- **Warning system**: Hiển thị cảnh báo khi gần đạt giới hạn
- **Responsive design**: Hoạt động trên tất cả kích thước thiết bị

## 🏗️ Kiến Trúc Hệ Thống

```
order-limit/
├── logger.ts                    # Entry point chính
├── config/
│   └── validation-config.ts     # Quy tắc và cài đặt giới hạn đặt hàng
├── services/
│   ├── i18n-service.ts         # Xử lý thông báo song ngữ
│   └── dom-service.ts          # Thao tác DOM và phát hiện trang
├── state/
│   └── validation-state.ts     # Quản lý state cho validation
├── validators/
│   └── order-limit-validator.ts # Logic validation cốt lõi
├── embedded.html               # Template HTML với styles
├── embedded.json              # Cấu hình Wix embedded script
├── params.dev.json           # Tham số development
├── tsconfig.json             # Cấu hình TypeScript
├── test-validation.html      # File test hệ thống
└── README.md                # Tài liệu hướng dẫn
```

## ⚙️ Cấu Hình

### Quy Tắc Giới Hạn Đặt Hàng

```typescript
interface OrderLimitRule {
  productId?: string;      // ID sản phẩm cụ thể
  categoryId?: string;     // ID danh mục sản phẩm  
  maxQuantity: number;     // Số lượng tối đa cho phép
  isGlobal?: boolean;      // Áp dụng cho tất cả sản phẩm
}
```

### Cài Đặt Validation

```typescript
interface ValidationSettings {
  enableProductPageValidation: boolean;  // Bật validation trang sản phẩm
  enableCartPageValidation: boolean;     // Bật validation trang giỏ hàng  
  enableCheckoutPageValidation: boolean; // Bật validation trang thanh toán
  showWarningThreshold: number;          // Hiển thị cảnh báo khi trong phạm vi N của giới hạn
  validationDelay: number;               // Delay debounce tính bằng milliseconds
}
```

## 🚀 Cách Sử Dụng

### 1. **Khởi Tạo Tự Động**
Hệ thống tự động khởi tạo khi trang được tải và phát hiện loại trang hiện tại.

### 2. **Phát Hiện Trang**
Tự động phát hiện nếu trang hiện tại là:
- Trang sản phẩm (`/product/`, `/product-page/`)
- Trang giỏ hàng (`/cart`, `/shopping-cart`)  
- Trang thanh toán (`/checkout`, `/payment`)

### 3. **Luồng Validation**

#### Trang Sản Phẩm:
1. Phát hiện thông tin sản phẩm (ID, tên, số lượng hiện tại)
2. Validation theo giới hạn đặt hàng
3. Chặn nút "Thêm Vào Giỏ" nếu vượt giới hạn
4. Hiển thị thông báo lỗi với giới hạn cụ thể

#### Trang Giỏ Hàng:
1. Trích xuất tất cả sản phẩm trong giỏ với số lượng
2. Validation từng sản phẩm theo giới hạn của nó
3. Chặn nút thanh toán nếu có vi phạm
4. Hiển thị tóm tắt lỗi validation

#### Trang Thanh Toán:
1. Validation cuối cùng của tất cả sản phẩm trong giỏ
2. Chặn các nút thanh toán/hoàn tất
3. Ngăn chặn submit form nếu có vi phạm
4. Hiển thị thông báo lỗi chặn

## 💬 Thông Báo Lỗi

### Thông Báo Tiếng Việt
- `Vượt quá giới hạn đặt hàng. Số lượng tối đa cho phép là {maxQuantity}.`
- `Số lượng quá cao cho sản phẩm "{productName}". Tối đa: {maxQuantity}, Hiện tại: {currentQuantity}.`
- `Không thể tiến hành thanh toán. Vui lòng điều chỉnh số lượng để đáp ứng giới hạn đặt hàng.`
- `Thanh toán bị chặn do vi phạm giới hạn đặt hàng.`

### Thông Báo Tiếng Anh  
- `Order limit exceeded. Maximum allowed quantity is {maxQuantity}.`
- `Quantity too high for product "{productName}". Maximum: {maxQuantity}, Current: {currentQuantity}.`
- `Cannot proceed to checkout. Please adjust quantities to meet order limits.`
- `Checkout is blocked due to order limit violations.`

## 🔧 Development & Testing

### Kiểm Tra Hệ Thống
1. **Mở file test**: `test-validation.html` trong trình duyệt
2. **Test trang sản phẩm**: Thay đổi số lượng input, quan sát validation
3. **Test trang giỏ hàng**: Thêm sản phẩm vào giỏ, sửa số lượng  
4. **Test trang thanh toán**: Tiến hành thanh toán với sản phẩm vượt giới hạn

### Chế Độ Debug
Khi chạy trên localhost hoặc môi trường dev, logging bổ sung được bật:

```javascript
console.log('Order Limit Validation System loaded for user: {{name}}');
console.log('Current page:', window.location.href);
console.log('Page type detection will be performed automatically');
```

### Cập Nhật Cấu Hình
Giới hạn đặt hàng có thể được cập nhật thông qua:
1. **localStorage**: `wix-order-limit-config`
2. **API integration**: Tích hợp backend trong tương lai
3. **Dashboard configuration**: Thông qua Wix app dashboard

## 🌐 Hỗ Trợ Trình Duyệt
- Trình duyệt hiện đại với hỗ trợ ES2017+
- Trình duyệt mobile (iOS Safari, Chrome Mobile)
- Trình duyệt desktop (Chrome, Firefox, Safari, Edge)

## ⚡ Hiệu Năng
- **Debounced validation**: Ngăn chặn validation call quá mức
- **Efficient DOM queries**: Selector được cache và lookup tối ưu
- **Memory management**: Cleanup đúng cách cho observers và event listeners
- **Lazy loading**: Chỉ validate khi cần thiết

## 🔒 Bảo Mật
- **Input validation**: Tất cả user input được validate và sanitize
- **XSS prevention**: Thực hành thao tác DOM an toàn
- **CSP compliance**: Tương thích với Content Security Policy

## 🚀 Cải Tiến Tương Lai
- [ ] Tích hợp backend API cho quản lý rule động
- [ ] Analytics và reporting nâng cao
- [ ] A/B testing cho chiến lược validation
- [ ] Tích hợp với Wix business solutions
- [ ] Custom validation rules cho từng merchant

## 📋 Hướng Dẫn Triển Khai

### Bước 1: Cài Đặt
```bash
# Clone repository
git clone <repository-url>
cd core-fe/src/site/embedded-scripts/order-limit

# Compile TypeScript
npx tsc
```

### Bước 2: Cấu Hình
1. Chỉnh sửa `config/validation-config.ts` để thiết lập quy tắc giới hạn
2. Tùy chỉnh thông báo trong `services/i18n-service.ts` nếu cần
3. Điều chỉnh cài đặt validation trong `ValidationSettings`

### Bước 3: Test
1. Mở `test-validation.html` trong trình duyệt
2. Test các scenario khác nhau:
   - Số lượng vượt giới hạn
   - Số lượng gần giới hạn  
   - Số lượng an toàn
   - Chuyển đổi ngôn ngữ

### Bước 4: Triển Khai
1. Upload các file đã compile lên Wix
2. Cấu hình embedded script trong Wix dashboard
3. Test trên môi trường production

## 🆘 Troubleshooting

### Lỗi Thường Gặp
1. **TypeScript compilation errors**: Kiểm tra `tsconfig.json` và đảm bảo target ES2017+
2. **Module not found**: Đảm bảo tất cả file đã được tạo trong đúng thư mục
3. **Validation không hoạt động**: Kiểm tra console log và đảm bảo page detection đúng

### Debug Tips
1. Mở Developer Tools và kiểm tra Console tab
2. Sử dụng `window.orderLimitSystem` để truy cập hệ thống từ console
3. Gọi `system.state.logCurrentState()` để xem trạng thái hiện tại

## 📞 Hỗ Trợ
Nếu gặp vấn đề, vui lòng:
1. Kiểm tra console log để xem lỗi cụ thể
2. Đảm bảo tất cả dependencies đã được cài đặt
3. Liên hệ team development để được hỗ trợ

// Order Limit Validation System
// Main entry point for embedded script validation

import { ValidationConfig } from "./config/validation-config";
import { DOMService } from "./services/dom-service";
import { I18nService } from "./services/i18n-service";
import { ValidationState } from "./state/validation-state";
import { OrderLimitValidator } from "./validators/order-limit-validator";

class OrderLimitSystem {
  private validator: OrderLimitValidator;
  private config: ValidationConfig;
  private i18n: I18nService;
  private domService: DOMService;
  private state: ValidationState;

  constructor() {
    this.config = new ValidationConfig();
    this.i18n = new I18nService();
    this.domService = new DOMService();
    this.state = new ValidationState();
    this.validator = new OrderLimitValidator(this.config, this.i18n, this.domService, this.state);
  }

  async initialize(): Promise<void> {
    try {
      console.log("Initializing Order Limit Validation System...");

      // Wait for DOM to be ready
      await this.domService.waitForDOM();

      // Initialize language detection
      await this.i18n.initialize();

      // Start validation based on current page
      await this.startValidation();

      console.log("Order Limit Validation System initialized successfully");
    } catch (error) {
      console.error("Failed to initialize Order Limit Validation System:", error);
    }
  }

  private async startValidation(): Promise<void> {
    const currentPage = this.domService.detectCurrentPage();

    switch (currentPage) {
      case "product":
        await this.validator.validateProductPage();
        break;
      case "cart":
        await this.validator.validateCartPage();
        break;
      case "checkout":
        await this.validator.validateCheckoutPage();
        break;
      default:
        console.log("No validation needed for current page:", currentPage);
    }
  }
}

// Initialize the system when DOM is loaded
if (document.readyState === "loading") {
  document.addEventListener("DOMContentLoaded", () => {
    const system = new OrderLimitSystem();
    system.initialize();
  });
} else {
  const system = new OrderLimitSystem();
  system.initialize();
}

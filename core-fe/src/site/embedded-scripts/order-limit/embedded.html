<!-- Order Limit Validation System -->
<!-- Embedded script for validating order limits across product, cart, and checkout pages -->

<style>
  /* Order Limit Validation Styles */
  .wix-order-limit-error {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell,
      sans-serif;
    line-height: 1.4;
    animation: slideIn 0.3s ease-out;
  }

  .wix-order-limit-error.warning {
    background-color: #ffa502 !important;
  }

  @keyframes slideIn {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Blocked element styles */
  [data-blocked-reason] {
    position: relative;
    cursor: not-allowed !important;
  }

  [data-blocked-reason]:hover::after {
    content: attr(data-blocked-reason);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 10000;
    pointer-events: none;
  }
</style>

<!-- Load the main validation system -->
<script type="module" src="./logger.ts"></script>

<!-- Debug information (only in development) -->
<script>
  // Add debug information for development
  if (window.location.hostname === "localhost" || window.location.hostname.includes("dev")) {
    console.log("Order Limit Validation System loaded for user: {{name}}");
    console.log("Current page:", window.location.href);
    console.log("Page type detection will be performed automatically");
  }
</script>

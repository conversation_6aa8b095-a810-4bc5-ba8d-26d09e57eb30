// Internationalization Service
// Handles bilingual error messages and language detection

export type Language = "en" | "vi";

export interface TranslationMessages {
  en: string;
  vi: string;
}

export interface ValidationMessages {
  orderLimitExceeded: TranslationMessages;
  quantityTooHigh: TranslationMessages;
  quantityTooLow: TranslationMessages;
  cannotProceedToCheckout: TranslationMessages;
  checkoutBlocked: TranslationMessages;
  adjustQuantity: TranslationMessages;
  adjustQuantityMin: TranslationMessages;
  validationError: TranslationMessages;
  loadingValidation: TranslationMessages;
  addToCartBlocked: TranslationMessages;
  cartValidationFailed: TranslationMessages;
  proceedWithCaution: TranslationMessages;
}

export class I18nService {
  private currentLanguage: Language = "en";
  private messages: ValidationMessages;

  constructor() {
    this.messages = {
      orderLimitExceeded: {
        en: "Order limit exceeded. Maximum allowed quantity is {maxQuantity}.",
        vi: "Vượt quá giới hạn đặt hàng. Số lượng tối đa cho phép là {maxQuantity}."
      },
      quantityTooHigh: {
        en: 'Quantity too high for product "{productName}". Maximum: {maxQuantity}, Current: {currentQuantity}.',
        vi: '<PERSON><PERSON> lượng quá cao cho sản phẩm "{productName}". Tối đa: {maxQuantity}, Hiện tại: {currentQuantity}.'
      },
      quantityTooLow: {
        en: 'Quantity too low for product "{productName}". Minimum: {minQuantity}, Current: {currentQuantity}.',
        vi: 'Số lượng quá thấp cho sản phẩm "{productName}". Tối thiểu: {minQuantity}, Hiện tại: {currentQuantity}.'
      },
      cannotProceedToCheckout: {
        en: "Cannot proceed to checkout. Please adjust quantities to meet order limits.",
        vi: "Không thể tiến hành thanh toán. Vui lòng điều chỉnh số lượng để đáp ứng giới hạn đặt hàng."
      },
      checkoutBlocked: {
        en: "Checkout is blocked due to order limit violations.",
        vi: "Thanh toán bị chặn do vi phạm giới hạn đặt hàng."
      },
      adjustQuantity: {
        en: "Please adjust the quantity to {maxQuantity} or less.",
        vi: "Vui lòng điều chỉnh số lượng xuống {maxQuantity} hoặc ít hơn."
      },
      adjustQuantityMin: {
        en: "Please adjust the quantity to {minQuantity} or more.",
        vi: "Vui lòng điều chỉnh số lượng lên {minQuantity} hoặc nhiều hơn."
      },
      validationError: {
        en: "Validation error occurred. Please try again.",
        vi: "Đã xảy ra lỗi xác thực. Vui lòng thử lại."
      },
      loadingValidation: {
        en: "Validating order limits...",
        vi: "Đang xác thực giới hạn đặt hàng..."
      },
      addToCartBlocked: {
        en: "Cannot add to cart. This would exceed the order limit of {maxQuantity}.",
        vi: "Không thể thêm vào giỏ hàng. Điều này sẽ vượt quá giới hạn đặt hàng {maxQuantity}."
      },
      cartValidationFailed: {
        en: "Cart validation failed. Some items exceed order limits.",
        vi: "Xác thực giỏ hàng thất bại. Một số mặt hàng vượt quá giới hạn đặt hàng."
      },
      proceedWithCaution: {
        en: "Proceed with caution. Some items are near their order limits.",
        vi: "Tiến hành một cách thận trọng. Một số mặt hàng gần đạt giới hạn đặt hàng."
      }
    };
  }

  async initialize(): Promise<void> {
    this.currentLanguage = this.detectLanguage();
    console.log("I18n service initialized with language:", this.currentLanguage);
  }

  private detectLanguage(): Language {
    // Try to detect language from various sources

    // 1. Check URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const langParam = urlParams.get("lang") || urlParams.get("language");
    if (langParam === "vi" || langParam === "en") {
      return langParam as Language;
    }

    // 2. Check HTML lang attribute
    const htmlLang = document.documentElement.lang;
    if (htmlLang.startsWith("vi")) {
      return "vi";
    }

    // 3. Check browser language
    const browserLang = navigator.language || (navigator as any).userLanguage;
    if (browserLang.startsWith("vi")) {
      return "vi";
    }

    // 4. Check localStorage
    const savedLang = localStorage.getItem("wix-order-limit-language");
    if (savedLang === "vi" || savedLang === "en") {
      return savedLang as Language;
    }

    // 5. Check for Vietnamese content in the page
    const pageText = document.body.textContent || "";
    const vietnamesePattern =
      /[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ]/i;
    if (vietnamesePattern.test(pageText)) {
      return "vi";
    }

    // Default to English
    return "en";
  }

  getCurrentLanguage(): Language {
    return this.currentLanguage;
  }

  setLanguage(language: Language): void {
    this.currentLanguage = language;
    localStorage.setItem("wix-order-limit-language", language);
  }

  getMessage(key: keyof ValidationMessages, params: Record<string, string | number> = {}): string {
    const message = this.messages[key][this.currentLanguage];

    // Replace parameters in the message
    return Object.entries(params).reduce((msg, [param, value]) => {
      return msg.replace(new RegExp(`\\{${param}\\}`, "g"), String(value));
    }, message);
  }

  // Get all messages for current language
  getAllMessages(): Record<keyof ValidationMessages, string> {
    const result = {} as Record<keyof ValidationMessages, string>;

    for (const key in this.messages) {
      result[key as keyof ValidationMessages] =
        this.messages[key as keyof ValidationMessages][this.currentLanguage];
    }

    return result;
  }

  // Add custom message
  addMessage(key: string, messages: TranslationMessages): void {
    (this.messages as any)[key] = messages;
  }
}

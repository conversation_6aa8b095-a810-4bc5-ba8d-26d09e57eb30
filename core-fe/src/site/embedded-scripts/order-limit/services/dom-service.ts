// DOM Service
// Handles DOM manipulation, page detection, and element interactions

export type PageType = "product" | "cart" | "checkout" | "other";

export interface ProductInfo {
  id: string;
  name: string;
  categoryId?: string;
  currentQuantity: number;
}

export interface CartItem {
  productId: string;
  productName: string;
  quantity: number;
  categoryId?: string;
}

export class DOMService {
  private observers: MutationObserver[] = [];

  constructor() {
    // Cleanup observers when page unloads
    window.addEventListener("beforeunload", () => {
      this.cleanup();
    });
  }

  async waitForDOM(): Promise<void> {
    return new Promise((resolve) => {
      if (document.readyState === "complete") {
        resolve();
      } else {
        window.addEventListener("load", () => resolve());
      }
    });
  }

  detectCurrentPage(): PageType {
    const url = window.location.href.toLowerCase();
    const pathname = window.location.pathname.toLowerCase();

    // Check URL patterns for different page types
    if (
      url.includes("/product/") ||
      pathname.includes("/product/") ||
      url.includes("/product-page/") ||
      pathname.includes("/product-page/")
    ) {
      return "product";
    }

    if (
      url.includes("/cart") ||
      pathname.includes("/cart") ||
      url.includes("/shopping-cart") ||
      pathname.includes("/shopping-cart")
    ) {
      return "cart";
    }

    if (
      url.includes("/checkout") ||
      pathname.includes("/checkout") ||
      url.includes("/payment") ||
      pathname.includes("/payment")
    ) {
      return "checkout";
    }

    // Check for specific Wix page indicators
    const pageElements = document.querySelectorAll(
      '[data-testid], [class*="product"], [class*="cart"], [class*="checkout"]'
    );
    for (let i = 0; i < pageElements.length; i++) {
      const element = pageElements[i];
      const testId = element.getAttribute("data-testid") || "";
      const className = element.className || "";

      if (testId.includes("product") || className.includes("product-page")) {
        return "product";
      }
      if (testId.includes("cart") || className.includes("cart-page")) {
        return "cart";
      }
      if (testId.includes("checkout") || className.includes("checkout-page")) {
        return "checkout";
      }
    }

    return "other";
  }

  // Product page methods
  getProductInfo(): ProductInfo | null {
    try {
      // Try multiple selectors to find product information
      const productSelectors = [
        '[data-testid*="product"]',
        '[class*="product-title"]',
        '[class*="product-name"]',
        "h1",
        ".product-title",
        ".product-name"
      ];

      let productName = "";
      for (const selector of productSelectors) {
        const element = document.querySelector(selector);
        if (element && element.textContent?.trim()) {
          productName = element.textContent.trim();
          break;
        }
      }

      // Try to get product ID from URL or data attributes
      const productId = this.extractProductId();

      // Get current quantity from quantity input
      const currentQuantity = this.getCurrentQuantityOnProductPage();

      if (productId && productName) {
        return {
          id: productId,
          name: productName,
          currentQuantity: currentQuantity
        };
      }

      return null;
    } catch (error) {
      console.error("Error getting product info:", error);
      return null;
    }
  }

  private extractProductId(): string {
    // Try to extract product ID from URL
    const urlMatch = window.location.href.match(/\/product\/([^\/\?]+)/);
    if (urlMatch) {
      return urlMatch[1];
    }

    // Try to find product ID in data attributes
    const productElements = document.querySelectorAll("[data-product-id], [data-id]");
    for (let i = 0; i < productElements.length; i++) {
      const element = productElements[i];
      const productId = element.getAttribute("data-product-id") || element.getAttribute("data-id");
      if (productId) {
        return productId;
      }
    }

    // Fallback: use current timestamp as unique identifier
    return `product_${Date.now()}`;
  }

  private getCurrentQuantityOnProductPage(): number {
    const quantitySelectors = [
      'input[type="number"]',
      '[data-testid*="quantity"] input',
      '[class*="quantity"] input',
      ".quantity-input",
      ".qty-input"
    ];

    for (const selector of quantitySelectors) {
      const input = document.querySelector(selector) as HTMLInputElement;
      if (input && input.value) {
        const quantity = parseInt(input.value, 10);
        if (!isNaN(quantity) && quantity > 0) {
          return quantity;
        }
      }
    }

    return 1; // Default quantity
  }

  getAddToCartButtons(): HTMLElement[] {
    const buttonSelectors = [
      '[data-testid*="add-to-cart"]',
      '[class*="add-to-cart"]',
      'button[class*="add"]',
      ".add-to-cart-button",
      ".add-to-cart"
    ];

    const buttons: HTMLElement[] = [];

    for (const selector of buttonSelectors) {
      const elements = document.querySelectorAll(selector);
      elements.forEach((element) => {
        if (element instanceof HTMLElement) {
          buttons.push(element);
        }
      });
    }

    // Also search by button text content
    const allButtons = document.querySelectorAll("button");
    allButtons.forEach((button) => {
      const text = button.textContent?.toLowerCase() || "";
      if (
        text.includes("add to cart") ||
        text.includes("thêm vào giỏ") ||
        text.includes("add to bag") ||
        text.includes("buy now")
      ) {
        buttons.push(button);
      }
    });

    return buttons;
  }

  // Cart page methods
  getCartItems(): CartItem[] {
    const items: CartItem[] = [];

    // Try to find cart item containers
    const itemSelectors = [
      '[data-testid*="cart-item"]',
      '[class*="cart-item"]',
      ".cart-item",
      ".line-item"
    ];

    for (const selector of itemSelectors) {
      const itemElements = document.querySelectorAll(selector);

      itemElements.forEach((itemElement, index) => {
        try {
          const productName = this.extractProductNameFromCartItem(itemElement);
          const quantity = this.extractQuantityFromCartItem(itemElement);
          const productId = this.extractProductIdFromCartItem(itemElement, index);

          if (productName && quantity > 0) {
            items.push({
              productId,
              productName,
              quantity
            });
          }
        } catch (error) {
          console.warn("Error extracting cart item info:", error);
        }
      });
    }

    return items;
  }

  private extractProductNameFromCartItem(itemElement: Element): string {
    const nameSelectors = [
      '[data-testid*="product-name"]',
      '[class*="product-name"]',
      '[class*="item-name"]',
      ".product-title",
      ".item-title",
      "h3",
      "h4"
    ];

    for (const selector of nameSelectors) {
      const nameElement = itemElement.querySelector(selector);
      if (nameElement && nameElement.textContent?.trim()) {
        return nameElement.textContent.trim();
      }
    }

    return `Product ${Date.now()}`;
  }

  private extractQuantityFromCartItem(itemElement: Element): number {
    const quantitySelectors = [
      'input[type="number"]',
      '[data-testid*="quantity"]',
      '[class*="quantity"]',
      ".qty"
    ];

    for (const selector of quantitySelectors) {
      const quantityElement = itemElement.querySelector(selector);
      if (quantityElement) {
        const value = (quantityElement as HTMLInputElement).value || quantityElement.textContent;
        if (value) {
          const quantity = parseInt(value.trim(), 10);
          if (!isNaN(quantity) && quantity > 0) {
            return quantity;
          }
        }
      }
    }

    return 1;
  }

  private extractProductIdFromCartItem(itemElement: Element, fallbackIndex: number): string {
    const productId =
      itemElement.getAttribute("data-product-id") ||
      itemElement.getAttribute("data-id") ||
      `cart_item_${fallbackIndex}`;
    return productId;
  }

  getCheckoutButtons(): HTMLElement[] {
    const buttonSelectors = [
      '[data-testid*="checkout"]',
      '[class*="checkout"]',
      '[class*="proceed"]',
      ".checkout-button",
      ".proceed-button",
      "button.buy-now-button"
    ];

    const buttons: HTMLElement[] = [];

    for (const selector of buttonSelectors) {
      const elements = document.querySelectorAll(selector);
      elements.forEach((element) => {
        if (element instanceof HTMLElement) {
          buttons.push(element);
        }
      });
    }

    // Search by button text
    const allButtons = document.querySelectorAll("button, a");
    const textCheckout = ["checkout", "thanh toán", "proceed", "continue"];
    allButtons.forEach((button) => {
      const text = button.textContent?.toLowerCase() || "";
      for (const textInclude of textCheckout) {
        if (text.includes(textInclude)) {
          buttons.push(button as HTMLElement);
          break;
        }
      }
    });

    return buttons;
  }

  // Utility methods
  blockElement(element: HTMLElement, reason: string): void {
    element.style.pointerEvents = "none";
    element.style.opacity = "0.5";
    element.setAttribute("disabled", "true");
    element.setAttribute("data-blocked-reason", reason);

    if (element.tagName === "BUTTON" || element.tagName === "INPUT") {
      (element as HTMLButtonElement | HTMLInputElement).disabled = true;
    }
  }

  unblockElement(element: HTMLElement): void {
    element.style.pointerEvents = "";
    element.style.opacity = "";
    element.removeAttribute("disabled");
    element.removeAttribute("data-blocked-reason");

    if (element.tagName === "BUTTON" || element.tagName === "INPUT") {
      (element as HTMLButtonElement | HTMLInputElement).disabled = false;
    }
  }

  showErrorMessage(message: string, targetElement?: HTMLElement): HTMLElement {
    // Check if the same message already exists to prevent duplicates
    const existingMessages = document.querySelectorAll(".wix-order-limit-error");
    for (const existing of existingMessages) {
      if (existing.textContent === message) {
        return existing as HTMLElement; // Return existing message instead of creating new one
      }
    }

    const errorDiv = document.createElement("div");
    errorDiv.className = "wix-order-limit-error";
    errorDiv.style.cssText = `
      background-color: #ff4757;
      color: white;
      padding: 12px 16px;
      border-radius: 4px;
      margin: 8px 0;
      font-size: 14px;
      font-weight: 500;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      z-index: 9999;
      position: relative;
    `;
    errorDiv.textContent = message;

    // Insert error message above the add to cart button
    if (targetElement && targetElement.parentNode) {
      targetElement.parentNode.insertBefore(errorDiv, targetElement);
    } else {
      // Find add to cart button and insert above it
      const addToCartButton = this.getAddToCartButtons()[0];
      if (addToCartButton && addToCartButton.parentNode) {
        addToCartButton.parentNode.insertBefore(errorDiv, addToCartButton);
      } else {
        document.body.insertBefore(errorDiv, document.body.firstChild);
      }
    }

    return errorDiv;
  }

  removeErrorMessages(): void {
    const errorMessages = document.querySelectorAll(".wix-order-limit-error");
    errorMessages.forEach((error) => error.remove());
  }

  observeElementChanges(element: HTMLElement, callback: () => void): void {
    const observer = new MutationObserver(callback);
    observer.observe(element, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ["value", "disabled"]
    });

    this.observers.push(observer);
  }

  cleanup(): void {
    this.observers.forEach((observer) => observer.disconnect());
    this.observers = [];
    this.removeErrorMessages();
  }
}
